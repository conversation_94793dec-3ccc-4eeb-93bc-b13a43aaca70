# 📝 Hướng dẫn siêu đơn giản cho bài thi

## 🚀 Khởi chạy n<PERSON> (30 giây)

```bash
npm install
npm run dev
```

Mở: http://localhost:5174

## 📋 Chức năng chính (4 chức năng duy nhất)

1. **Xem danh sách**: Trang chính hiển thị tất cả todos
2. **Thêm todo**: Click "Add New Todo" → điền form → auto redirect về danh sách
3. **Edit todo**: Click "Edit" trên card → chuyển trang edit → auto redirect về danh sách
4. **Delete todo**: Click "Delete" trên card → confirm popup → xóa ngay

## 🏗️ Cấu trúc code CỰC ĐƠN GIẢN

### Files chính (chỉ 6 files):
- `src/App.jsx` - Router với 4 routes
- `src/pages/TodoList.jsx` - Trang chính
- `src/pages/TodoAdd.jsx` - Trang thêm todo
- `src/pages/TodoUpdate.jsx` - Trang edit todo
- `src/components/TodoCard.jsx` - Card với 3 buttons
- `src/store/todosSlice.js` - Redux slice

### Routes (chỉ 4 routes):
```javascript
<Routes>
  <Route path='*' element={<TodoList />} />
  <Route path='/' element={<TodoList />} />
  <Route path='/add' element={<TodoAdd />} />
  <Route path='/update/:id' element={<TodoUpdate />} />
</Routes>
```

## 🔧 Các thao tác

### TodoCard có 3 buttons:
- **Green**: Complete/Undo (toggle trạng thái)
- **Blue**: Edit (chuyển trang edit)
- **Red**: Delete (confirm popup)

### User Flow:
```
TodoList (/)
├── Click "Add New Todo" → /add → Save → Auto redirect to /
├── Click "Edit" on card → /update/:id → Save → Auto redirect to /
├── Click "Delete" on card → Confirm → Delete → Stay on same page
└── Click "Complete/Undo" → Update in place
```

## 💡 Tại sao đơn giản nhất?

1. **Không có**: TodoDelete page, Navigation menu, complex components
2. **Không có**: Complex routing, nested routes
3. **Chỉ có**: 4 routes đơn giản, separate edit page, direct delete
4. **Tập trung**: Core CRUD functionality
5. **Thời gian**: Có thể code trong 20-25 phút

## 🎯 Perfect cho thi vì:

- ✅ Ít code nhất có thể
- ✅ Ít bugs nhất có thể
- ✅ Dễ explain nhất có thể
- ✅ Vẫn đầy đủ CRUD
- ✅ Vẫn có React Router
- ✅ Vẫn có Redux
- ✅ Vẫn có API integration

## 🚨 Lưu ý quan trọng

- **API**: MockAPI tự động lưu trữ trên cloud
- **State**: Redux quản lý toàn bộ state
- **Styling**: Inline styles đơn giản
- **Validation**: Chỉ check title không empty

**Kết luận**: Đây là version tối giản nhất - hoàn hảo cho bài thi!
