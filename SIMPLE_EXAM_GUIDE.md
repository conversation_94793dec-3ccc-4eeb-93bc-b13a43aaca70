# 📝 Hướng dẫn siêu đơn giản cho bài thi

## 🚀 Khởi chạy n<PERSON> (30 giây)

```bash
npm install
npm run dev
```

Mở: http://localhost:5174

## 📋 Chức năng chính (3 chức năng duy nhất)

1. **Xem danh sách**: Trang chính hiển thị tất cả todos
2. **Thêm todo**: Click "Add New Todo" → điền form → auto redirect về danh sách  
3. **Edit/Delete todo**: Trực tiếp trên card, không cần chuyển trang

## 🏗️ Cấu trúc code CỰC ĐƠN GIẢN

### Files chính (chỉ 5 files):
- `src/App.jsx` - Router với 3 routes
- `src/pages/TodoList.jsx` - Trang chính
- `src/pages/TodoAdd.jsx` - Trang thêm todo
- `src/components/TodoCard.jsx` - Card với inline edit
- `src/store/todosSlice.js` - Redux slice

### Routes (chỉ 3 routes):
```javascript
<Routes>
  <Route path='*' element={<TodoList />} />
  <Route path='/' element={<TodoList />} />
  <Route path='/add' element={<TodoAdd />} />
</Routes>
```

## 🔧 Các thao tác

### TodoCard có 3 buttons:
- **Green**: Complete/Undo (toggle trạng thái)
- **Blue**: Edit (inline edit trực tiếp trên card)
- **Red**: Delete (confirm popup)

### User Flow:
```
TodoList (/) 
├── Click "Add New Todo" → /add → Save → Auto redirect to /
├── Click "Edit" on card → Edit inline → Save → Stay on same page
├── Click "Delete" on card → Confirm → Delete → Stay on same page
└── Click "Complete/Undo" → Update in place
```

## 💡 Tại sao đơn giản nhất?

1. **Không có**: TodoUpdate page, TodoDelete page, Navigation menu
2. **Không có**: Complex routing, nested routes
3. **Chỉ có**: 3 routes, inline editing, direct delete
4. **Tập trung**: Core CRUD functionality
5. **Thời gian**: Có thể code trong 15-20 phút

## 🎯 Perfect cho thi vì:

- ✅ Ít code nhất có thể
- ✅ Ít bugs nhất có thể  
- ✅ Dễ explain nhất có thể
- ✅ Vẫn đầy đủ CRUD
- ✅ Vẫn có React Router
- ✅ Vẫn có Redux
- ✅ Vẫn có API integration

## 🚨 Lưu ý quan trọng

- **API**: MockAPI tự động lưu trữ trên cloud
- **State**: Redux quản lý toàn bộ state
- **Styling**: Inline styles đơn giản
- **Validation**: Chỉ check title không empty

**Kết luận**: Đây là version tối giản nhất - hoàn hảo cho bài thi!
