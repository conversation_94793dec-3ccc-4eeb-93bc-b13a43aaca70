# 📝 Hướng dẫn nhanh cho bài thi

## 🚀 Khởi chạy nhanh

```bash
npm install
npm run dev
```

Mở: http://localhost:5173

## 📋 Chức năng chính

1. **Xem danh sách**: Trang chính hiển thị tất cả todos
2. **Thêm todo**: Click "Add New Todo" → điền form → auto redirect về danh sách
3. **Sửa todo**: Click "Edit" trên card → điền form → auto redirect về danh sách
4. **Xóa todo**: Click "Delete" trên card → trang xác nhận → auto redirect về danh sách
5. **Complete/Undo**: Click nút "Complete"/"Undo" trực tiếp trên card

## 🏗️ Cấu trúc code đơn giản

### Components chính:
- `src/App.jsx` - Component gốc với Router (5 routes cực đơn giản)
- `src/pages/TodoList.jsx` - <PERSON><PERSON> chính (danh sách todos)
- `src/pages/TodoAdd.jsx` - Trang thêm todo
- `src/pages/TodoUpdate.jsx` - Trang sửa todo
- `src/pages/TodoDelete.jsx` - Trang xóa todo
- `src/components/TodoCard.jsx` - Card hiển thị todo với buttons
- `src/components/TodoForm.jsx` - Form component (nếu cần)

### Redux:
- `src/store/store.js` - Redux store
- `src/store/todosSlice.js` - Todos slice với API calls

### API:
- MockAPI endpoint: `https://67cd350bdd7651e464eda2a9.mockapi.io/todos`
- Tự động lưu trữ dữ liệu trên cloud

## 🔧 Các thao tác API

- **GET** `/todos` - Lấy danh sách
- **POST** `/todos` - Thêm mới
- **PUT** `/todos/:id` - Cập nhật
- **DELETE** `/todos/:id` - Xóa

## 💡 Tips cho bài thi

1. **Cực đơn giản**: Chỉ 5 routes (/, /add, /update/:id, /delete/:id)
2. **Auto-navigation**: Mọi action đều tự động redirect về trang chính
3. **Minimal styling**: Chỉ đủ để test, không mất thời gian CSS
4. **CRUD hoàn chỉnh**: Create, Read, Update, Delete
5. **Redux**: Sử dụng Redux Toolkit hiện đại
6. **Nhanh**: Perfect cho thi cử, tiết kiệm thời gian tối đa

## 🎯 Điểm mạnh để trình bày

- ✅ React functional components với hooks
- ✅ Redux Toolkit cho state management
- ✅ React Router DOM cho navigation
- ✅ Async operations với createAsyncThunk
- ✅ API integration với axios
- ✅ Error handling cơ bản
- ✅ Loading states
- ✅ Form validation
- ✅ Statistics page với data visualization
- ✅ Responsive design đơn giản

## 🚨 Lưu ý

- Không cần setup database
- Không cần authentication
- UI đơn giản, tập trung vào functionality
- Code clean và dễ hiểu
- Phù hợp cho demo trong thời gian ngắn
