import { Provider } from 'react-redux'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { store } from './store/store'
import TodoList from './pages/TodoList'
import TodoAdd from './pages/TodoAdd'
import TodoUpdate from './pages/TodoUpdate'

function App() {
  return (
    <Provider store={store}>
      <Router>
        <Routes>
          <Route path='*' element={<TodoList />} />
          <Route path='/' element={<TodoList />} />
          <Route path='/add' element={<TodoAdd />} />
          <Route path='/update/:id' element={<TodoUpdate />} />
        </Routes>
      </Router>
    </Provider>
  )
}

export default App
