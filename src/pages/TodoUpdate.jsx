import { useState, useEffect } from 'react'
import { use<PERSON>arams, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { fetchTodos, updateTodo } from '../store/todosSlice'

const TodoUpdate = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { items: todos, loading } = useSelector(state => state.todos)

  const todo = todos.find(t => t.id === id)

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium',
    completed: false
  })

  useEffect(() => {
    if (todos.length === 0) {
      dispatch(fetchTodos())
    }
  }, [dispatch, todos.length])

  useEffect(() => {
    if (todo) {
      setFormData({
        title: todo.title || '',
        description: todo.description || '',
        priority: todo.priority || 'medium',
        completed: todo.completed || false
      })
    }
  }, [todo])

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!formData.title.trim()) return

    try {
      const updates = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        priority: formData.priority,
        completed: formData.completed
      }

      await dispatch(updateTodo({
        id: id,
        updates: updates,
        currentTodo: todo
      })).unwrap()

      navigate('/')
    } catch (error) {
      console.error('Update todo failed:', error)
      navigate('/')
    }
  }

  if (loading && !todo) {
    return <div>Loading...</div>
  }

  if (!todo) {
    return (
      <div>
        <h1>Todo Not Found</h1>
        <button onClick={() => navigate('/')}>Back to List</button>
      </div>
    )
  }

  return (
    <div style={{ padding: '20px', maxWidth: '500px', margin: '0 auto' }}>
      <h1>Edit Todo</h1>

      <form onSubmit={handleSubmit} style={{ border: '1px solid #ddd', padding: '20px' }}>
        <div style={{ marginBottom: '15px' }}>
          <label>Title:</label>
          <input
            type="text"
            name="title"
            value={formData.title}
            onChange={handleChange}
            style={{ width: '100%', padding: '8px', marginTop: '5px' }}
            required
          />
        </div>

        <div style={{ marginBottom: '15px' }}>
          <label>Description:</label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            style={{ width: '100%', padding: '8px', marginTop: '5px', height: '60px' }}
          />
        </div>

        <div style={{ marginBottom: '15px' }}>
          <label>Priority:</label>
          <select
            name="priority"
            value={formData.priority}
            onChange={handleChange}
            style={{ width: '100%', padding: '8px', marginTop: '5px' }}
          >
            <option value="low">Low Priority</option>
            <option value="medium">Medium Priority</option>
            <option value="high">High Priority</option>
          </select>
        </div>

        <div style={{ marginBottom: '15px' }}>
          <label>
            <input
              type="checkbox"
              name="completed"
              checked={formData.completed}
              onChange={handleChange}
              style={{ marginRight: '8px' }}
            />
            Completed
          </label>
        </div>

        <div>
          <button
            type="submit"
            style={{
              padding: '10px 20px',
              backgroundColor: 'blue',
              color: 'white',
              border: 'none',
              marginRight: '10px'
            }}
          >
            Update
          </button>
          <button
            type="button"
            onClick={() => navigate('/')}
            style={{
              padding: '10px 20px',
              backgroundColor: 'gray',
              color: 'white',
              border: 'none'
            }}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  )
}

export default TodoUpdate
