import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { addTodo } from '../store/todosSlice'

const TodoAdd = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  // const { loading } = useSelector(state => state.todos)

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium'
  })

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!formData.title.trim()) return

    try {
      const todoData = {
        ...formData,
        title: formData.title.trim(),
        description: formData.description.trim(),
        completed: false,
        createdAt: new Date().toISOString()
      }

      await dispatch(addTodo(todoData)).unwrap()
      navigate('/')
    } catch (error) {
      console.error('Add todo failed:', error)
      navigate('/')
    }
  }

  return (
    <div style={{ padding: '20px', maxWidth: '500px', margin: '0 auto' }}>
      <h1>Add New Todo</h1>

      <form onSubmit={handleSubmit} style={{ border: '1px solid #ddd', padding: '20px' }}>
        <div style={{ marginBottom: '15px' }}>
          <label>Title:</label>
          <input
            type="text"
            name="title"
            value={formData.title}
            onChange={handleChange}
            style={{ width: '100%', padding: '8px', marginTop: '5px' }}
            required
          />
        </div>

        <div style={{ marginBottom: '15px' }}>
          <label>Description:</label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            style={{ width: '100%', padding: '8px', marginTop: '5px', height: '60px' }}
          />
        </div>

        <div style={{ marginBottom: '20px' }}>
          <label>Priority:</label>
          <select
            name="priority"
            value={formData.priority}
            onChange={handleChange}
            style={{ width: '100%', padding: '8px', marginTop: '5px' }}
          >
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
          </select>
        </div>

        <div>
          <button
            type="submit"
            style={{
              padding: '10px 20px',
              backgroundColor: 'green',
              color: 'white',
              border: 'none',
              marginRight: '10px'
            }}
          >
            Add Todo
          </button>
          <button
            type="button"
            onClick={() => navigate('/')}
            style={{
              padding: '10px 20px',
              backgroundColor: 'gray',
              color: 'white',
              border: 'none'
            }}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  )
}

export default TodoAdd
