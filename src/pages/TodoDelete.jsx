import { useParams, useNavigate } from 'react-router-dom'
import { useSelector, useDispatch } from 'react-redux'
import { useEffect } from 'react'
import { fetchTodos, deleteTodo } from '../store/todosSlice'

const TodoDelete = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { items: todos, loading } = useSelector(state => state.todos)
  
  const todo = todos.find(t => t.id === id)

  useEffect(() => {
    if (todos.length === 0) {
      dispatch(fetchTodos())
    }
  }, [dispatch, todos.length])

  const handleDelete = async () => {
    try {
      await dispatch(deleteTodo(id)).unwrap()
      navigate('/')
    } catch (error) {
      console.error('Delete failed:', error)
      navigate('/')
    }
  }

  const handleCancel = () => {
    navigate('/')
  }

  if (loading) {
    return <div>Loading...</div>
  }

  if (!todo) {
    return (
      <div>
        <h1>Todo Not Found</h1>
        <button onClick={() => navigate('/')}>Back to List</button>
      </div>
    )
  }

  return (
    <div style={{ padding: '20px', maxWidth: '500px', margin: '0 auto' }}>
      <h1>Delete Todo</h1>
      
      <div style={{ border: '1px solid #ddd', padding: '20px', marginBottom: '20px' }}>
        <h3>{todo.title}</h3>
        {todo.description && <p>{todo.description}</p>}
        <p>Priority: {todo.priority || 'medium'}</p>
        <p>Status: {todo.completed ? 'Completed' : 'Pending'}</p>
      </div>

      <p style={{ color: 'red', marginBottom: '20px' }}>
        Are you sure you want to delete this todo? This action cannot be undone.
      </p>

      <div>
        <button 
          onClick={handleDelete}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: 'red', 
            color: 'white', 
            border: 'none',
            marginRight: '10px'
          }}
        >
          Delete
        </button>
        <button 
          onClick={handleCancel}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: 'gray', 
            color: 'white', 
            border: 'none'
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  )
}

export default TodoDelete
