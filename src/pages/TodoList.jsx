import { useDispatch, useSelector } from 'react-redux'
import { useEffect } from 'react'
import { Link } from 'react-router-dom'
import { fetchTodos } from '../store/todosSlice'
import TodoCard from '../components/TodoCard'

const TodoList = () => {
  const dispatch = useDispatch()
  const { items: todos, loading, error } = useSelector(state => state.todos)

  useEffect(() => {
    dispatch(fetchTodos())
  }, [dispatch])

  if (loading) {
    return <div style={{ padding: '20px' }}>Loading...</div>
  }

  if (error) {
    return <div style={{ padding: '20px', color: 'red' }}>Error: {error}</div>
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h1>Todo List ({todos.length})</h1>
        <Link 
          to="/add" 
          style={{
            padding: '10px 20px',
            backgroundColor: 'green',
            color: 'white',
            textDecoration: 'none',
            borderRadius: '4px'
          }}
        >
          Add New Todo
        </Link>
      </div>

      {todos.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <p>No todos yet!</p>
          <Link to="/add" style={{ color: 'blue' }}>Add your first todo</Link>
        </div>
      ) : (
        <div>
          {todos.map((todo) => (
            <TodoCard key={todo.id} todo={todo} />
          ))}
        </div>
      )}
    </div>
  )
}

export default TodoList
