import { useNavigate } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { updateTodo } from '../store/todosSlice'

const TodoCard = ({ todo }) => {
  const navigate = useNavigate()
  const dispatch = useDispatch()

  const handleToggleComplete = async () => {
    try {
      await dispatch(updateTodo({
        id: todo.id,
        updates: { completed: !todo.completed },
        currentTodo: todo
      })).unwrap()
    } catch (error) {
      console.error('Toggle failed:', error)
    }
  }

  const handleEdit = () => {
    navigate(`/update/${todo.id}`)
  }

  const handleDelete = () => {
    navigate(`/delete/${todo.id}`)
  }

  return (
    <div style={{
      border: '1px solid #ddd',
      padding: '15px',
      margin: '10px 0',
      backgroundColor: todo.completed ? '#f0f0f0' : 'white'
    }}>
      <h3 style={{
        textDecoration: todo.completed ? 'line-through' : 'none',
        color: todo.completed ? '#666' : 'black'
      }}>
        {todo.title}
      </h3>

      {todo.description && <p>{todo.description}</p>}

      <p>Priority: {todo.priority || 'medium'}</p>
      <p>Status: {todo.completed ? 'Completed' : 'Pending'}</p>

      <div style={{ marginTop: '10px' }}>
        <button
          onClick={handleToggleComplete}
          style={{
            padding: '8px 15px',
            backgroundColor: todo.completed ? 'orange' : 'green',
            color: 'white',
            border: 'none',
            marginRight: '10px'
          }}
        >
          {todo.completed ? 'Undo' : 'Complete'}
        </button>

        <button
          onClick={handleEdit}
          style={{
            padding: '8px 15px',
            backgroundColor: 'blue',
            color: 'white',
            border: 'none',
            marginRight: '10px'
          }}
        >
          Edit
        </button>

        <button
          onClick={handleDelete}
          style={{
            padding: '8px 15px',
            backgroundColor: 'red',
            color: 'white',
            border: 'none'
          }}
        >
          Delete
        </button>
      </div>
    </div>
  )
}

export default TodoCard
