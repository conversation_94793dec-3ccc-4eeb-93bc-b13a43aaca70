import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { updateTodo } from '../store/todosSlice'

const TodoCard = ({ todo }) => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    title: todo.title || '',
    description: todo.description || '',
    priority: todo.priority || 'medium'
  })

  const handleToggleComplete = async () => {
    try {
      await dispatch(updateTodo({
        id: todo.id,
        updates: { completed: !todo.completed },
        currentTodo: todo
      })).unwrap()
    } catch (error) {
      console.error('Toggle failed:', error)
    }
  }

  const handleEdit = () => {
    navigate(`/update/${todo.id}`)
  }

  const handleDelete = () => {
    navigate(`/delete/${todo.id}`)
  }

  const handleInlineEdit = () => {
    setEditForm({
      title: todo.title || '',
      description: todo.description || '',
      priority: todo.priority || 'medium'
    })
    setIsEditing(true)
  }

  const handleSaveEdit = async () => {
    if (!editForm.title.trim()) return

    try {
      await dispatch(updateTodo({
        id: todo.id,
        updates: {
          title: editForm.title.trim(),
          description: editForm.description.trim(),
          priority: editForm.priority
        },
        currentTodo: todo
      })).unwrap()
      setIsEditing(false)
    } catch (error) {
      console.error('Update failed:', error)
      setIsEditing(false)
    }
  }

  const handleCancelEdit = () => {
    setEditForm({
      title: todo.title || '',
      description: todo.description || '',
      priority: todo.priority || 'medium'
    })
    setIsEditing(false)
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setEditForm(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <div style={{
      border: '1px solid #ddd',
      padding: '15px',
      margin: '10px 0',
      backgroundColor: todo.completed ? '#f0f0f0' : 'white'
    }}>
      {isEditing ? (
        // Edit Mode
        <div>
          <div style={{ marginBottom: '10px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Title:</label>
            <input
              type="text"
              name="title"
              value={editForm.title}
              onChange={handleInputChange}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid #ccc',
                borderRadius: '4px'
              }}
              required
            />
          </div>

          <div style={{ marginBottom: '10px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Description:</label>
            <textarea
              name="description"
              value={editForm.description}
              onChange={handleInputChange}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                height: '60px',
                resize: 'vertical'
              }}
            />
          </div>

          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Priority:</label>
            <select
              name="priority"
              value={editForm.priority}
              onChange={handleInputChange}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid #ccc',
                borderRadius: '4px'
              }}
            >
              <option value="low">Low Priority</option>
              <option value="medium">Medium Priority</option>
              <option value="high">High Priority</option>
            </select>
          </div>

          <div style={{ marginTop: '15px' }}>
            <button
              onClick={handleSaveEdit}
              style={{
                padding: '8px 15px',
                backgroundColor: 'green',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                marginRight: '10px'
              }}
            >
              Save
            </button>
            <button
              onClick={handleCancelEdit}
              style={{
                padding: '8px 15px',
                backgroundColor: 'gray',
                color: 'white',
                border: 'none',
                borderRadius: '4px'
              }}
            >
              Cancel
            </button>
          </div>
        </div>
      ) : (
        // View Mode
        <div>
          <h3 style={{
            textDecoration: todo.completed ? 'line-through' : 'none',
            color: todo.completed ? '#666' : 'black'
          }}>
            {todo.title}
          </h3>

          {todo.description && <p>{todo.description}</p>}

          <p>Priority: {todo.priority || 'medium'}</p>
          <p>Status: {todo.completed ? 'Completed' : 'Pending'}</p>

          <div style={{ marginTop: '10px' }}>
            <button
              onClick={handleToggleComplete}
              style={{
                padding: '8px 15px',
                backgroundColor: todo.completed ? 'orange' : 'green',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                marginRight: '10px'
              }}
            >
              {todo.completed ? 'Undo' : 'Complete'}
            </button>

            <button
              onClick={handleInlineEdit}
              style={{
                padding: '8px 15px',
                backgroundColor: 'blue',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                marginRight: '10px'
              }}
            >
              Quick Edit
            </button>

            <button
              onClick={handleEdit}
              style={{
                padding: '8px 15px',
                backgroundColor: 'purple',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                marginRight: '10px'
              }}
            >
              Full Edit
            </button>

            <button
              onClick={handleDelete}
              style={{
                padding: '8px 15px',
                backgroundColor: 'red',
                color: 'white',
                border: 'none',
                borderRadius: '4px'
              }}
            >
              Delete
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default TodoCard
